import Cocoa
import Foundation

/// Трекер общего времени за компьютером
/// Отслеживает активность пользователя каждую минуту и сохраняет данные о времени активности
class ComputerTimeTracker {
    
    // MARK: - Properties
    
    private var isTracking = false
    private var checkTimer: Timer?
    private var lastActivityTime = Date()
    private var activityThreshold: TimeInterval = 1.0  // Минимум 1 секунда между проверками
    
    // Мониторы событий
    private var mouseMonitor: Any?
    private var keyboardMonitor: Any?
    
    // Статистика
    private let statisticsManager: StatisticsManager

    // Callback для уведомления о записанной активности
    var onActivityRecorded: ((Bool) -> Void)?

    // MARK: - Initialization
    
    init(statisticsManager: StatisticsManager) {
        self.statisticsManager = statisticsManager
    }
    
    // MARK: - Public Methods
    
    /// Начинает отслеживание времени за компьютером
    func startTracking() {
        guard !isTracking else { return }
        
        print("💻 ComputerTimeTracker: Начинаем отслеживание времени за компьютером")
        isTracking = true
        lastActivityTime = Date()
        
        // Запускаем мониторинг активности
        startActivityMonitoring()
        
        // Запускаем таймер проверки каждую минуту
        checkTimer = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: true) { [weak self] _ in
            self?.checkActivityForMinute()
        }
    }
    
    /// Останавливает отслеживание времени за компьютером
    func stopTracking() {
        guard isTracking else { return }
        
        print("💻 ComputerTimeTracker: Останавливаем отслеживание времени за компьютером")
        isTracking = false
        
        // Останавливаем мониторинг активности
        stopActivityMonitoring()
        
        // Останавливаем таймер
        checkTimer?.invalidate()
        checkTimer = nil
    }
    
    // MARK: - Private Methods
    
    private func startActivityMonitoring() {
        // Мониторинг движений мыши (только значительные движения)
        mouseMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.mouseMoved, .leftMouseDown, .rightMouseDown, .scrollWheel]) { [weak self] event in
            self?.handleMouseActivity(event)
        }
        
        // Мониторинг клавиатуры
        keyboardMonitor = NSEvent.addGlobalMonitorForEvents(matching: [.keyDown]) { [weak self] event in
            self?.handleKeyboardActivity(event)
        }
    }
    
    private func stopActivityMonitoring() {
        if let mouseMonitor = mouseMonitor {
            NSEvent.removeMonitor(mouseMonitor)
            self.mouseMonitor = nil
        }
        
        if let keyboardMonitor = keyboardMonitor {
            NSEvent.removeMonitor(keyboardMonitor)
            self.keyboardMonitor = nil
        }
    }
    
    private func handleMouseActivity(_ event: NSEvent) {
        // Фильтруем только значительные движения мыши
        if event.type == .mouseMoved {
            // Проверяем, что движение достаточно значительное
            let deltaX = abs(event.deltaX)
            let deltaY = abs(event.deltaY)
            
            // Игнорируем микро-движения (случайные толчки мыши)
            if deltaX < 5 && deltaY < 5 {
                return
            }
        }
        
        markActivity()
    }
    
    private func handleKeyboardActivity(_ event: NSEvent) {
        markActivity()
    }
    
    private func markActivity() {
        let now = Date()
        let timeSinceLastActivity = now.timeIntervalSince(lastActivityTime)
        
        // Обновляем время последней активности только если прошло достаточно времени
        if timeSinceLastActivity >= activityThreshold {
            lastActivityTime = now
            // Не логируем каждую активность, чтобы не засорять консоль
        }
    }
    
    /// Проверяет активность за последнюю минуту и записывает результат
    private func checkActivityForMinute() {
        let oneMinuteAgo = Date().addingTimeInterval(-60)
        let wasActiveThisMinute = lastActivityTime > oneMinuteAgo

        if wasActiveThisMinute {
            // Записываем активную минуту
            statisticsManager.recordActiveMinute()
            print("💻 ComputerTimeTracker: Активная минута зафиксирована")
        } else {
            print("💻 ComputerTimeTracker: Минута без активности")
        }

        // Уведомляем детектор неформальных сессий о активности
        onActivityRecorded?(wasActiveThisMinute)
    }
    
    deinit {
        stopTracking()
    }
}
