# 🎯 Концепция "Приоритетный проект" для решения проблемы прокрастинации

## 📋 Описание проблемы

**Основная проблема:** Пользователь не может начать и поддерживать регулярную работу над приоритетным проектом. Постоянно отвлекается на срочные дела, не может сосредоточиться на главном.

**Симптомы:**
- Сидит за компьютером, но не работает над важным проектом
- Отвлекается на мелкие задачи
- Не может войти в рабочий ритм
- Откладывает начало работы над приоритетным проектом

## 🎯 Концепция решения

### 1. **Priority Project (Приоритетный проект)**

**Основная идея:** Один проект = главный фокус

**Функциональность:**
- Пользователь выбирает один проект как "целевой/приоритетный"
- Визуальное выделение в списке проектов (звездочка, рамка, специальная иконка)
- Возможность смены целевого проекта
- Хранение в UserDefaults или в данных ProjectManager

**Техническая реализация:**
```swift
// В ProjectManager
private var priorityProjectId: UUID?

func setPriorityProject(_ projectId: UUID)
func getPriorityProject() -> Project?
func isPriorityProject(_ projectId: UUID) -> Bool
```

### 2. **Расширенная система стриков и аналитики**

**Стрики именно для целевого проекта:**
- Не общие интервалы, а работа именно по выбранному проекту
- Отслеживание "дней без работы по целевому проекту"
- Расширение WorkPatternAnalyzer для анализа паттернов работы по целевому проекту

**Новые метрики:**
- Дни подряд работы по приоритетному проекту
- Процент времени, потраченного на приоритетный проект
- Средняя задержка начала работы по приоритетному проекту

**Техническая реализация:**
```swift
// В WorkPatternAnalyzer
func analyzePriorityProjectPattern(projectId: UUID) -> PriorityProjectPattern
func getDaysWithoutPriorityWork(projectId: UUID) -> Int
func getPriorityProjectStreak(projectId: UUID) -> Int
```

### 3. **Система напоминаний и мотивации**

**Умные напоминания:**
- При запуске приложения: "Не забудьте о проекте X!"
- Регулярные напоминания, если долго не работал по целевому проекту
- Расширение MotivationManager для работы с целевым проектом
- Специальные мотивационные сообщения для приоритетного проекта

**Типы напоминаний:**
1. **При запуске приложения** - если вчера не работал по приоритетному проекту
2. **Через 30 минут после запуска** - если еще не начал работать
3. **Каждые 2 часа** - если в течение дня не работал по приоритетному проекту
4. **Вечернее напоминание** - планирование на завтра

**Техническая реализация:**
```swift
// В MotivationManager
func checkPriorityProjectReminders()
func sendPriorityProjectStartReminder()
func sendPriorityProjectProgressReminder()
```

### 4. **Адаптивные интервалы**

**Динамическое сокращение времени:**
- Если не работал по целевому проекту 1-2 дня → интервал 40 минут
- Если не работал 3-5 дней → интервал 25 минут  
- Если не работал неделю → интервал 15 минут (для легкого входа)
- Постепенное восстановление до 52 минут при регулярной работе

**Логика адаптации:**
```swift
func getAdaptiveIntervalDuration(for priorityProjectId: UUID) -> TimeInterval {
    let daysWithoutWork = getDaysWithoutPriorityWork(priorityProjectId)
    
    switch daysWithoutWork {
    case 0: return 52 * 60  // Стандартный интервал
    case 1...2: return 40 * 60  // Легкое сокращение
    case 3...5: return 25 * 60  // Значительное сокращение
    case 6...7: return 15 * 60  // Минимальный интервал для входа
    default: return 10 * 60     // Критическое сокращение
    }
}
```

### 5. **Помощь с входом в поток**

**Система преодоления барьеров:**
- Простые диалоги: "Что мешает начать?" с вариантами ответов
- Предложения микро-задач: "Начните с 5 минут планирования"
- Мотивационные цитаты специально для целевого проекта
- Напоминание о прогрессе по проекту

**Типовые барьеры и решения:**
1. **"Не знаю, с чего начать"** → Предложить 5-минутное планирование
2. **"Задача слишком большая"** → Разбить на микро-задачи
3. **"Нет настроения"** → Предложить начать с 10 минут
4. **"Отвлекают другие дела"** → Напомнить о приоритетах

## 🏗️ Техническая архитектура

### Новые компоненты:

1. **PriorityProjectManager**
   - Управление приоритетным проектом
   - Отслеживание метрик по приоритетному проекту
   - Интеграция с существующими системами

2. **AdaptiveIntervalManager**
   - Расчет адаптивной длительности интервалов
   - Логика восстановления стандартной длительности
   - Интеграция с PomodoroTimer

3. **FlowAssistant**
   - Помощь с преодолением барьеров
   - Предложение микро-задач
   - Диалоги мотивации

### Расширения существующих компонентов:

1. **ProjectManager**
   - Добавить концепцию приоритетного проекта
   - Методы для работы с приоритетным проектом

2. **WorkPatternAnalyzer**
   - Анализ работы по приоритетному проекту
   - Новые метрики и риски

3. **MotivationManager**
   - Напоминания для приоритетного проекта
   - Специальные мотивационные сообщения

4. **PomodoroTimer**
   - Поддержка адаптивной длительности интервалов
   - Интеграция с AdaptiveIntervalManager

## 🎨 UI/UX изменения

### 1. **Окно проектов**
- Возможность выбора приоритетного проекта (звездочка или специальная иконка)
- Визуальное выделение приоритетного проекта
- Статистика по приоритетному проекту

### 2. **Настройки**
- Настройки адаптивных интервалов
- Настройки напоминаний для приоритетного проекта
- Настройки помощи с входом в поток

### 3. **Новые уведомления**
- Специальные уведомления для приоритетного проекта
- Диалоги преодоления барьеров
- Предложения микро-задач

### 4. **Статистика**
- Отдельная секция для приоритетного проекта
- Стрики по приоритетному проекту
- Прогресс и тренды

## 🚀 Пользовательский сценарий

1. **Настройка:**
   - Пользователь выбирает проект "Изучение Swift" как приоритетный
   - Настраивает параметры напоминаний

2. **Ежедневное использование:**
   - Запускает приложение → получает напоминание о приоритетном проекте
   - Если не работал 2 дня → интервал автоматически сокращается до 40 минут
   - При попытке начать интервал → предложение работать по приоритетному проекту

3. **Преодоление барьеров:**
   - Если долго не начинает → диалог "Что мешает начать?"
   - Предложение микро-задач: "Потратьте 5 минут на планирование"
   - Мотивационные сообщения с учетом прогресса

4. **Отслеживание прогресса:**
   - Видит стрики по приоритетному проекту
   - Получает еженедельные отчеты о прогрессе
   - Система адаптируется к его рабочим паттернам

## 📊 Ожидаемые результаты

- **Увеличение времени работы по приоритетному проекту** на 40-60%
- **Сокращение времени "раскачки"** перед началом работы
- **Повышение регулярности работы** над важными проектами
- **Снижение прокрастинации** за счет адаптивных интервалов и напоминаний

## 🔄 Этапы реализации

### Этап 1: Основа
- Концепция приоритетного проекта в ProjectManager
- Базовые напоминания
- Простая адаптация интервалов

### Этап 2: Аналитика
- Расширенная аналитика по приоритетному проекту
- Стрики и метрики
- Интеграция с существующей системой анализа

### Этап 3: Помощь с входом в поток
- Диалоги преодоления барьеров
- Предложения микро-задач
- Мотивационная система

### Этап 4: Полировка
- Улучшение UI/UX
- Тонкая настройка алгоритмов
- Дополнительные функции на основе обратной связи
