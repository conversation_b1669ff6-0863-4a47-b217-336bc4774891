import Foundation

/// Детектор неформальных рабочих сессий
/// Отслеживает длительную работу без запущенного Pomodoro-интервала
/// и предлагает пользователю сделать перерыв
class InformalSessionDetector {
    
    // MARK: - Properties
    
    /// История активности по минутам (true = активная минута, false = неактивная)
    private var minuteActivityLog: [Bool] = []
    
    /// Максимальный размер лога (храним больше чем анализируем для запаса)
    private let maxLogSize = 60
    
    /// Размер окна для анализа (последние N минут)
    private let checkWindowSize = 52

    /// Минимальное количество активных минут для срабатывания
    private let minActiveMinutes = 42 // 80% из 52 минут
    
    /// Время последнего показа предложения отдыха
    private var lastSuggestionTime: Date?
    
    /// Период "охлаждения" - минимальное время между предложениями отдыха
    private let cooldownPeriod: TimeInterval = 30 * 60 // 30 минут
    
    /// Включен ли детектор
    private var isEnabled = true
    
    // MARK: - Dependencies
    
    private weak var pomodoroTimer: PomodoroTimer?
    private weak var breakTimer: BreakTimer?
    
    // MARK: - Callbacks
    
    /// Вызывается когда нужно показать предложение отдыха
    var onRestSuggestionNeeded: (() -> Void)?
    
    // MARK: - Initialization
    
    init(pomodoroTimer: PomodoroTimer, breakTimer: BreakTimer) {
        self.pomodoroTimer = pomodoroTimer
        self.breakTimer = breakTimer
        
        print("🔍 InformalSessionDetector: Инициализирован")
    }
    
    // MARK: - Public Methods
    
    /// Записывает активность за минуту
    /// - Parameter isActive: была ли активность в эту минуту
    func recordMinuteActivity(isActive: Bool) {
        guard isEnabled else { return }
        
        minuteActivityLog.append(isActive)
        
        // Ограничиваем размер лога
        if minuteActivityLog.count > maxLogSize {
            minuteActivityLog.removeFirst()
        }
        
        let activeCount = minuteActivityLog.filter { $0 }.count
        print("🔍 InformalSessionDetector: Записана активность \(isActive ? "ДА" : "НЕТ"). Лог: \(minuteActivityLog.count) мин, активных: \(activeCount)")
        
        // Проверяем только если накопилось достаточно данных
        if minuteActivityLog.count >= checkWindowSize {
            checkForRestSuggestion()
        }
    }
    
    /// Сбрасывает историю активности (например, при начале формального интервала)
    func resetActivityHistory() {
        minuteActivityLog.removeAll()
        print("🔍 InformalSessionDetector: История активности сброшена")
    }
    
    /// Включает/выключает детектор
    func setEnabled(_ enabled: Bool) {
        isEnabled = enabled
        if !enabled {
            resetActivityHistory()
        }
        print("🔍 InformalSessionDetector: \(enabled ? "Включен" : "Выключен")")
    }
    
    /// Сбрасывает cooldown (для тестирования)
    func resetCooldown() {
        lastSuggestionTime = nil
        print("🔍 InformalSessionDetector: Cooldown сброшен")
    }

    /// Заполняет историю активными минутами для тестирования
    func fillWithActiveMinutesForTesting() {
        minuteActivityLog.removeAll()
        // Заполняем 52 активными минутами
        for _ in 0..<checkWindowSize {
            minuteActivityLog.append(true)
        }
        print("🔍 InformalSessionDetector: Заполнено \(checkWindowSize) активными минутами для тестирования")

        // Сразу проверяем условия
        checkForRestSuggestion()
    }
    
    // MARK: - Private Methods
    
    /// Проверяет условия для показа предложения отдыха
    private func checkForRestSuggestion() {
        // 1. Проверяем, что приложение в состоянии покоя
        guard isAppIdle() else {
            print("🔍 InformalSessionDetector: Приложение не в состоянии покоя, пропускаем проверку")
            return
        }
        
        // 2. Проверяем cooldown
        guard canShowSuggestion() else {
            print("🔍 InformalSessionDetector: Cooldown еще не истек, пропускаем")
            return
        }
        
        // 3. Анализируем активность за последние N минут
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count
        
        print("🔍 InformalSessionDetector: Анализ последних \(checkWindowSize) минут: \(activeCount) активных из \(recentActivity.count)")
        
        if activeCount >= minActiveMinutes {
            print("🔍 InformalSessionDetector: ✅ Условия выполнены! Показываем предложение отдыха")
            showRestSuggestion()
        } else {
            print("🔍 InformalSessionDetector: Недостаточно активности (\(activeCount) < \(minActiveMinutes))")
        }
    }
    
    /// Проверяет, находится ли приложение в состоянии покоя
    private func isAppIdle() -> Bool {
        // Проверяем состояние Pomodoro таймера
        guard let timer = pomodoroTimer, timer.state == .idle else {
            return false
        }
        
        // Проверяем состояние отдыха
        guard let breakTimer = breakTimer, !breakTimer.isActive else {
            return false
        }
        
        return true
    }
    
    /// Проверяет, можно ли показать предложение (cooldown)
    private func canShowSuggestion() -> Bool {
        guard let lastTime = lastSuggestionTime else {
            return true // Первый раз
        }
        
        let timeSinceLastSuggestion = Date().timeIntervalSince(lastTime)
        return timeSinceLastSuggestion >= cooldownPeriod
    }
    
    /// Показывает предложение отдыха
    private func showRestSuggestion() {
        lastSuggestionTime = Date()
        onRestSuggestionNeeded?()
        
        print("🔍 InformalSessionDetector: 🎯 Предложение отдыха показано")
    }
    
    // MARK: - Debug Methods
    
    /// Возвращает текущую статистику для отладки
    func getDebugInfo() -> String {
        let recentActivity = Array(minuteActivityLog.suffix(checkWindowSize))
        let activeCount = recentActivity.filter { $0 }.count
        let totalMinutes = minuteActivityLog.count
        
        var info = "InformalSessionDetector Debug:\n"
        info += "- Всего минут в логе: \(totalMinutes)\n"
        info += "- Активных в последних \(checkWindowSize): \(activeCount)/\(recentActivity.count)\n"
        info += "- Требуется для срабатывания: \(minActiveMinutes)\n"
        info += "- Приложение в покое: \(isAppIdle())\n"
        info += "- Можно показать предложение: \(canShowSuggestion())\n"
        info += "- Включен: \(isEnabled)"
        
        return info
    }
}
